import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env file.')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Database types for TypeScript
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          subscription_status: 'free' | 'premium' | 'admin'
          subscription_expires_at: string | null
          stripe_customer_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          subscription_status?: 'free' | 'premium' | 'admin'
          subscription_expires_at?: string | null
          stripe_customer_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          subscription_status?: 'free' | 'premium' | 'admin'
          subscription_expires_at?: string | null
          stripe_customer_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      projects: {
        Row: {
          id: string
          user_id: string
          name: string
          description: string | null
          custom_instructions: string | null
          is_public: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          description?: string | null
          custom_instructions?: string | null
          is_public?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          description?: string | null
          custom_instructions?: string | null
          is_public?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      chats: {
        Row: {
          id: string
          user_id: string
          project_id: string | null
          title: string
          last_message_preview: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          project_id?: string | null
          title: string
          last_message_preview?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          project_id?: string | null
          title?: string
          last_message_preview?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      chat_messages: {
        Row: {
          id: string
          chat_id: string
          role: 'user' | 'assistant' | 'system'
          content: string
          metadata: Record<string, unknown>
          created_at: string
        }
        Insert: {
          id?: string
          chat_id: string
          role: 'user' | 'assistant' | 'system'
          content: string
          metadata?: Record<string, unknown>
          created_at?: string
        }
        Update: {
          id?: string
          chat_id?: string
          role?: 'user' | 'assistant' | 'system'
          content?: string
          metadata?: Record<string, unknown>
          created_at?: string
        }
      }
      project_files: {
        Row: {
          id: string
          project_id: string
          user_id: string
          filename: string
          original_filename: string
          file_type: string
          file_size: number
          storage_path: string
          extracted_content: string | null
          metadata: Record<string, unknown>
          created_at: string
        }
        Insert: {
          id?: string
          project_id: string
          user_id: string
          filename: string
          original_filename: string
          file_type: string
          file_size: number
          storage_path: string
          extracted_content?: string | null
          metadata?: Record<string, unknown>
          created_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          user_id?: string
          filename?: string
          original_filename?: string
          file_type?: string
          file_size?: number
          storage_path?: string
          extracted_content?: string | null
          metadata?: Record<string, unknown>
          created_at?: string
        }
      }
      chat_files: {
        Row: {
          id: string
          chat_id: string
          project_file_id: string
          created_at: string
        }
        Insert: {
          id?: string
          chat_id: string
          project_file_id: string
          created_at?: string
        }
        Update: {
          id?: string
          chat_id?: string
          project_file_id?: string
          created_at?: string
        }
      }
      prompt_templates: {
        Row: {
          id: string
          name: string
          description: string | null
          template_content: string
          is_system_template: boolean
          user_id: string | null
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          template_content: string
          is_system_template?: boolean
          user_id?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          template_content?: string
          is_system_template?: boolean
          user_id?: string | null
          created_at?: string
        }
      }
      subscription_plans: {
        Row: {
          id: string
          name: string
          description: string | null
          price_monthly: number
          price_yearly: number | null
          stripe_price_id_monthly: string | null
          stripe_price_id_yearly: string | null
          features: Record<string, unknown>
          limits: Record<string, unknown>
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          price_monthly: number
          price_yearly?: number | null
          stripe_price_id_monthly?: string | null
          stripe_price_id_yearly?: string | null
          features?: Record<string, unknown>
          limits?: Record<string, unknown>
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          price_monthly?: number
          price_yearly?: number | null
          stripe_price_id_monthly?: string | null
          stripe_price_id_yearly?: string | null
          features?: Record<string, unknown>
          limits?: Record<string, unknown>
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      user_subscriptions: {
        Row: {
          id: string
          user_id: string
          plan_id: string
          stripe_customer_id: string | null
          stripe_subscription_id: string | null
          status: string
          current_period_start: string | null
          current_period_end: string | null
          cancel_at_period_end: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          plan_id: string
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          status?: string
          current_period_start?: string | null
          current_period_end?: string | null
          cancel_at_period_end?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          plan_id?: string
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          status?: string
          current_period_start?: string | null
          current_period_end?: string | null
          cancel_at_period_end?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      user_usage: {
        Row: {
          id: string
          user_id: string
          month_year: string
          prompts_generated: number
          projects_created: number
          api_calls: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          month_year: string
          prompts_generated?: number
          projects_created?: number
          api_calls?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          month_year?: string
          prompts_generated?: number
          projects_created?: number
          api_calls?: number
          created_at?: string
          updated_at?: string
        }
      }
      master_templates: {
        Row: {
          id: string
          created_by: string
          title: string
          description: string | null
          category: string
          content: Record<string, unknown>
          required_plan: string
          is_active: boolean
          usage_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          created_by: string
          title: string
          description?: string | null
          category: string
          content: Record<string, unknown>
          required_plan?: string
          is_active?: boolean
          usage_count?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          created_by?: string
          title?: string
          description?: string | null
          category?: string
          content?: Record<string, unknown>
          required_plan?: string
          is_active?: boolean
          usage_count?: number
          created_at?: string
          updated_at?: string
        }
      }
      project_access: {
        Row: {
          id: string
          project_id: string
          user_id: string
          granted_by: string
          access_type: string
          expires_at: string | null
          created_at: string
        }
        Insert: {
          id?: string
          project_id: string
          user_id: string
          granted_by: string
          access_type?: string
          expires_at?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          user_id?: string
          granted_by?: string
          access_type?: string
          expires_at?: string | null
          created_at?: string
        }
      }
      payment_history: {
        Row: {
          id: string
          user_id: string
          stripe_payment_intent_id: string | null
          amount: number
          currency: string
          status: string
          description: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          stripe_payment_intent_id?: string | null
          amount: number
          currency?: string
          status: string
          description?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          stripe_payment_intent_id?: string | null
          amount?: number
          currency?: string
          status?: string
          description?: string | null
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      subscription_status: 'free' | 'premium' | 'admin'
    }
  }
}

// Convenience type aliases
export type Project = Database['public']['Tables']['projects']['Row']
export type ProjectInsert = Database['public']['Tables']['projects']['Insert']
export type ProjectUpdate = Database['public']['Tables']['projects']['Update']
export type Profile = Database['public']['Tables']['profiles']['Row']
export type Chat = Database['public']['Tables']['chats']['Row']
export type ChatInsert = Database['public']['Tables']['chats']['Insert']
export type ChatUpdate = Database['public']['Tables']['chats']['Update']
export type Message = Database['public']['Tables']['chat_messages']['Row']
export type MessageInsert = Database['public']['Tables']['chat_messages']['Insert']
export type MessageUpdate = Database['public']['Tables']['chat_messages']['Update']
export type ProjectFile = Database['public']['Tables']['project_files']['Row']
export type ProjectFileInsert = Database['public']['Tables']['project_files']['Insert']
export type ProjectFileUpdate = Database['public']['Tables']['project_files']['Update']
export type ChatFile = Database['public']['Tables']['chat_files']['Row']
export type ChatFileInsert = Database['public']['Tables']['chat_files']['Insert']
export type ChatFileUpdate = Database['public']['Tables']['chat_files']['Update']
export type PromptTemplate = Database['public']['Tables']['prompt_templates']['Row']
export type PromptTemplateInsert = Database['public']['Tables']['prompt_templates']['Insert']
export type PromptTemplateUpdate = Database['public']['Tables']['prompt_templates']['Update']
export type SubscriptionPlan = Database['public']['Tables']['subscription_plans']['Row']
export type SubscriptionPlanInsert = Database['public']['Tables']['subscription_plans']['Insert']
export type SubscriptionPlanUpdate = Database['public']['Tables']['subscription_plans']['Update']
export type UserSubscription = Database['public']['Tables']['user_subscriptions']['Row']
export type UserSubscriptionInsert = Database['public']['Tables']['user_subscriptions']['Insert']
export type UserSubscriptionUpdate = Database['public']['Tables']['user_subscriptions']['Update']
export type UserUsage = Database['public']['Tables']['user_usage']['Row']
export type UserUsageInsert = Database['public']['Tables']['user_usage']['Insert']
export type UserUsageUpdate = Database['public']['Tables']['user_usage']['Update']
export type MasterTemplate = Database['public']['Tables']['master_templates']['Row']
export type MasterTemplateInsert = Database['public']['Tables']['master_templates']['Insert']
export type MasterTemplateUpdate = Database['public']['Tables']['master_templates']['Update']
export type ProjectAccess = Database['public']['Tables']['project_access']['Row']
export type ProjectAccessInsert = Database['public']['Tables']['project_access']['Insert']
export type ProjectAccessUpdate = Database['public']['Tables']['project_access']['Update']
export type PaymentHistory = Database['public']['Tables']['payment_history']['Row']
export type PaymentHistoryInsert = Database['public']['Tables']['payment_history']['Insert']
export type PaymentHistoryUpdate = Database['public']['Tables']['payment_history']['Update']

// Extended types with relations
export interface ChatWithMessages extends Chat {
  messages: Message[]
  project?: Project
  files?: ProjectFile[]
}

export interface ProjectWithChats extends Project {
  chats: Chat[]
  files: ProjectFile[]
}

export interface MessageWithMetadata extends Message {
  isGenerating?: boolean
  error?: string
}

// Legacy types for backward compatibility with old project structure
export interface ProjectContent {
  userIdea: string
  selectedStyle: string
  generatedPrompt: string
  iterations: ProjectIteration[]
  metadata: {
    wordCount: number
    characterCount: number
    tags: string[]
    isFavorite: boolean
  }
}

export interface ProjectIteration {
  id: string
  userIdea: string
  selectedStyle: string
  generatedPrompt: string
  timestamp: string
}
