import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useProjects } from '@/hooks/useProjects';
import { projectUtils } from '@/lib/projects';
import { toast } from 'sonner';
import { Loader2, Save, Edit } from 'lucide-react';

interface SaveProjectDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userIdea: string;
  selectedStyle: string;
  generatedPrompt: string;
  currentProjectId?: string;
  onProjectSaved: (projectId: string) => void;
}

const themeCategories = [
  { value: '', label: 'No Category' },
  { value: 'marketing', label: 'Marketing' },
  { value: 'technical', label: 'Technical' },
  { value: 'creative', label: 'Creative Writing' },
  { value: 'business', label: 'Business' },
  { value: 'education', label: 'Education' },
  { value: 'research', label: 'Research' },
  { value: 'personal', label: 'Personal' },
];

const SaveProjectDialog: React.FC<SaveProjectDialogProps> = ({
  open,
  onOpenChange,
  userIdea,
  selectedStyle,
  generatedPrompt,
  currentProjectId,
  onProjectSaved
}) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [themeCategory, setThemeCategory] = useState('');
  const [isPublic, setIsPublic] = useState(false);
  const [saving, setSaving] = useState(false);
  const { createProject, updateProject, addIteration } = useProjects();

  const isUpdate = !!currentProjectId;

  useEffect(() => {
    if (open && !isUpdate) {
      // Generate default title for new projects
      const defaultTitle = projectUtils.generateDefaultTitle(userIdea);
      setTitle(defaultTitle);
      setDescription('');
      setThemeCategory('');
      setIsPublic(false);
    }
  }, [open, userIdea, isUpdate]);

  const handleSave = async () => {
    // Validate form
    if (!title.trim()) {
      toast.error('Project title is required');
      return;
    }

    const projectData = {
      title: title.trim(),
      description: description.trim() || undefined,
      userIdea,
      selectedStyle,
      generatedPrompt,
      themeCategory: themeCategory || undefined,
      isPublic
    };

    const validation = projectUtils.validateProjectData(projectData);
    if (!validation.isValid) {
      toast.error(validation.errors[0]);
      return;
    }

    setSaving(true);

    try {
      let projectId: string;

      if (isUpdate && currentProjectId) {
        // Add iteration to existing project
        const updatedProject = await addIteration(
          currentProjectId,
          userIdea,
          selectedStyle,
          generatedPrompt
        );
        
        if (!updatedProject) {
          throw new Error('Failed to add iteration');
        }
        
        projectId = updatedProject.id;
      } else {
        // Create new project
        const newProject = await createProject(projectData);
        
        if (!newProject) {
          throw new Error('Failed to create project');
        }
        
        projectId = newProject.id;
      }

      onProjectSaved(projectId);
    } catch (error) {
      console.error('Error saving project:', error);
      toast.error('Failed to save project');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
    setTitle('');
    setDescription('');
    setThemeCategory('');
    setIsPublic(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {isUpdate ? (
              <>
                <Edit className="w-5 h-5" />
                Add Iteration to Project
              </>
            ) : (
              <>
                <Save className="w-5 h-5" />
                Save New Project
              </>
            )}
          </DialogTitle>
          <DialogDescription>
            {isUpdate 
              ? 'Add this prompt as a new iteration to your existing project.'
              : 'Save your generated prompt as a new project for future reference.'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {!isUpdate && (
            <>
              <div className="space-y-2">
                <Label htmlFor="title">Project Title *</Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Enter project title..."
                  maxLength={100}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Optional description of your project..."
                  rows={3}
                  maxLength={500}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select value={themeCategory} onValueChange={setThemeCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category..." />
                  </SelectTrigger>
                  <SelectContent>
                    {themeCategories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="public"
                  checked={isPublic}
                  onCheckedChange={setIsPublic}
                />
                <Label htmlFor="public" className="text-sm">
                  Make this project public (others can view it)
                </Label>
              </div>
            </>
          )}

          {/* Preview of what will be saved */}
          <div className="border rounded-lg p-3 bg-gray-50">
            <h4 className="font-medium text-sm text-gray-700 mb-2">
              {isUpdate ? 'New Iteration Preview:' : 'Project Preview:'}
            </h4>
            <div className="space-y-1 text-xs text-gray-600">
              <p><span className="font-medium">Style:</span> {selectedStyle}</p>
              <p><span className="font-medium">Idea:</span> {userIdea.substring(0, 100)}{userIdea.length > 100 ? '...' : ''}</p>
              <p><span className="font-medium">Prompt Length:</span> {generatedPrompt.length} characters</p>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={handleCancel} disabled={saving}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            {saving && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
            {isUpdate ? 'Add Iteration' : 'Save Project'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SaveProjectDialog;
