import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useProjects } from '@/hooks/useProjects';
import { useAuth } from '@/contexts/AuthContext';
import { projectUtils } from '@/lib/projects';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import BackgroundEffects from '@/components/BackgroundEffects';
import Logo from '@/components/Logo';
import EditProjectDialog from '@/components/EditProjectDialog';
import { 
  Search, 
  Filter, 
  Plus, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Heart, 
  HeartOff, 
  Eye, 
  Calendar,
  FileText,
  ArrowLeft,
  SortAsc,
  SortDesc
} from 'lucide-react';
import { toast } from 'sonner';

const themeCategories = [
  { value: '', label: 'All Categories' },
  { value: 'marketing', label: 'Marketing' },
  { value: 'technical', label: 'Technical' },
  { value: 'creative', label: 'Creative Writing' },
  { value: 'business', label: 'Business' },
  { value: 'education', label: 'Education' },
  { value: 'research', label: 'Research' },
  { value: 'personal', label: 'Personal' },
];

const sortOptions = [
  { value: 'updated_at', label: 'Last Modified' },
  { value: 'created_at', label: 'Date Created' },
  { value: 'title', label: 'Title' },
];

const Projects: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated, user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [sortBy, setSortBy] = useState('updated_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);
  const [editingProject, setEditingProject] = useState<any>(null);

  const filters = useMemo(() => ({
    search: searchQuery,
    themeCategory: selectedCategory || undefined,
    sortBy: sortBy as 'created_at' | 'updated_at' | 'title',
    sortOrder,
    limit: 50
  }), [searchQuery, selectedCategory, sortBy, sortOrder]);

  const { 
    projects, 
    loading, 
    error, 
    deleteProject, 
    toggleFavorite,
    refreshProjects 
  } = useProjects(filters);

  // Filter projects for favorites if needed
  const filteredProjects = useMemo(() => {
    if (!showFavoritesOnly) return projects;
    return projects.filter(project => {
      const content = (project as any).content;
      return content && content.metadata?.isFavorite;
    });
  }, [projects, showFavoritesOnly]);

  const handleDeleteProject = async (projectId: string) => {
    const success = await deleteProject(projectId);
    if (success) {
      toast.success('Project deleted successfully');
    }
  };

  const handleToggleFavorite = async (projectId: string) => {
    await toggleFavorite(projectId);
  };

  const handleViewProject = (projectId: string) => {
    navigate(`/projects/${projectId}`);
  };

  const handleEditProject = (project: any) => {
    setEditingProject(project);
  };

  const handleProjectUpdated = (updatedProject: any) => {
    setEditingProject(null);
    refreshProjects();
  };

  const toggleSortOrder = () => {
    setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc');
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        <BackgroundEffects />
        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <Card className="w-full max-w-md mx-4">
            <CardHeader className="text-center">
              <CardTitle>Sign In Required</CardTitle>
              <CardDescription>
                Please sign in to view your projects
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <Button onClick={() => navigate('/auth')}>
                Sign In
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      <BackgroundEffects />
      
      <div className="relative z-10">
        {/* Header */}
        <header className="flex justify-between items-center p-6">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Generator
            </Button>
            <Logo />
          </div>
        </header>

        {/* Main Content */}
        <main className="container max-w-6xl mx-auto px-6 pb-16">
          <div className="mb-8">
            <h1 className="text-4xl font-bold tracking-tight mb-2">
              My <span className="text-highlight">Projects</span>
            </h1>
            <p className="text-xl text-gray-600">
              Manage and organize your saved prompts
            </p>
          </div>

          {/* Filters and Search */}
          <div className="mb-8 space-y-4">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search projects..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Category Filter */}
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  {themeCategories.map((category) => (
                    <SelectItem 
                      key={category.value} 
                      value={category.value || "all"}
                    >
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Sort */}
              <div className="flex gap-2">
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-full sm:w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {sortOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Button
                  variant="outline"
                  size="icon"
                  onClick={toggleSortOrder}
                  className="flex-shrink-0"
                >
                  {sortOrder === 'asc' ? <SortAsc className="w-4 h-4" /> : <SortDesc className="w-4 h-4" />}
                </Button>
              </div>
            </div>

            {/* Filter Toggles */}
            <div className="flex gap-2">
              <Button
                variant={showFavoritesOnly ? "default" : "outline"}
                size="sm"
                onClick={() => setShowFavoritesOnly(!showFavoritesOnly)}
                className="flex items-center gap-2"
              >
                <Heart className="w-4 h-4" />
                Favorites Only
              </Button>
            </div>
          </div>

          {/* Projects Grid */}
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <Card key={i} className="h-64">
                  <CardHeader>
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-4 w-full mb-2" />
                    <Skeleton className="h-4 w-2/3" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : error ? (
            <Card className="text-center py-12">
              <CardContent>
                <p className="text-red-600 mb-4">Failed to load projects</p>
                <Button onClick={refreshProjects}>Try Again</Button>
              </CardContent>
            </Card>
          ) : filteredProjects.length === 0 ? (
            <Card className="text-center py-12">
              <CardContent>
                <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">No Projects Found</h3>
                <p className="text-gray-600 mb-6">
                  {searchQuery || selectedCategory || showFavoritesOnly
                    ? 'No projects match your current filters.'
                    : 'Start creating prompts to see your projects here.'}
                </p>
                <Button onClick={() => navigate('/')} className="flex items-center gap-2">
                  <Plus className="w-4 h-4" />
                  Create Your First Project
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredProjects.map((project) => (
                <ProjectCard
                  key={project.id}
                  project={project}
                  onView={() => handleViewProject(project.id)}
                  onEdit={() => handleEditProject(project)}
                  onDelete={() => handleDeleteProject(project.id)}
                  onToggleFavorite={() => handleToggleFavorite(project.id)}
                />
              ))}
            </div>
          )}
        </main>

        {/* Edit Project Dialog */}
        <EditProjectDialog
          open={!!editingProject}
          onOpenChange={(open) => !open && setEditingProject(null)}
          project={editingProject}
          onProjectUpdated={handleProjectUpdated}
        />
      </div>
    </div>
  );
};

interface ProjectCardProps {
  project: any;
  onView: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onToggleFavorite: () => void;
}

const ProjectCard: React.FC<ProjectCardProps> = ({
  project,
  onView,
  onEdit,
  onDelete,
  onToggleFavorite
}) => {
  const stats = projectUtils.getProjectStats(project);
  const formattedDate = projectUtils.formatDate(project.updated_at);

  return (
    <Card className="group hover:shadow-lg transition-all duration-300 cursor-pointer" onClick={onView}>
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg truncate">{(project as any).title || project.name}</CardTitle>
            <CardDescription className="text-sm">
              {project.description || 'No description'}
            </CardDescription>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
              <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
                <MoreVertical className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onView(); }}>
                <Eye className="w-4 h-4 mr-2" />
                View
              </DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onEdit(); }}>
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onToggleFavorite(); }}>
                {stats.isFavorite ? (
                  <>
                    <HeartOff className="w-4 h-4 mr-2" />
                    Remove from Favorites
                  </>
                ) : (
                  <>
                    <Heart className="w-4 h-4 mr-2" />
                    Add to Favorites
                  </>
                )}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem 
                    className="text-red-600 focus:text-red-600"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete Project</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to delete "{project.title}"? This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={onDelete} className="bg-red-600 hover:bg-red-700">
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="flex items-center gap-2 mt-2">
          {project.theme_category && (
            <Badge variant="secondary" className="text-xs">
              {project.theme_category}
            </Badge>
          )}
          {stats.isFavorite && (
            <Heart className="w-4 h-4 text-red-500 fill-current" />
          )}
          {project.is_public && (
            <Badge variant="outline" className="text-xs">
              Public
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            <span>{formattedDate}</span>
          </div>
          
          <div className="flex justify-between text-xs">
            <span>{stats.wordCount} words</span>
            <span>{stats.iterationCount} iterations</span>
          </div>
          
          <p className="text-xs text-gray-500 line-clamp-2">
            {project.content?.userIdea || 'No content available'}
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default Projects;



