import { supabase, Project, ProjectInsert, ProjectUpdate, ProjectContent, ProjectIteration } from './supabase';
import { toast } from 'sonner';

export interface CreateProjectData {
  title: string;
  description?: string;
  userIdea: string;
  selectedStyle: string;
  generatedPrompt: string;
  themeCategory?: string;
  isPublic?: boolean;
}

export interface ProjectFilters {
  search?: string;
  themeCategory?: string;
  isPublic?: boolean;
  sortBy?: 'created_at' | 'updated_at' | 'name';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export class ProjectsService {
  /**
   * Create a new project
   */
  static async createProject(data: CreateProjectData): Promise<{ project: Project | null; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { project: null, error: new Error('User not authenticated') };
      }

      const projectContent: ProjectContent = {
        userIdea: data.userIdea,
        selectedStyle: data.selectedStyle,
        generatedPrompt: data.generatedPrompt,
        iterations: [],
        metadata: {
          wordCount: data.generatedPrompt.split(' ').length,
          characterCount: data.generatedPrompt.length,
          tags: [],
          isFavorite: false
        }
      };

      const projectData: ProjectInsert = {
        user_id: user.id,
        name: data.title,
        description: data.description || null,
        content: projectContent,
        theme_category: data.themeCategory || null,
        is_public: data.isPublic || false
      };

      const { data: project, error } = await supabase
        .from('projects')
        .insert(projectData)
        .select()
        .single();

      if (error) {
        console.error('Error creating project:', error);
        return { project: null, error: new Error(error.message) };
      }

      return { project, error: null };
    } catch (error) {
      console.error('Error creating project:', error);
      return { project: null, error: error as Error };
    }
  }

  /**
   * Get user's projects with optional filtering
   */
  static async getUserProjects(filters: ProjectFilters = {}): Promise<{ projects: Project[]; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { projects: [], error: new Error('User not authenticated') };
      }

      let query = supabase
        .from('projects')
        .select('*')
        .eq('user_id', user.id);

      // Apply filters
      if (filters.search) {
        query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
      }

      if (filters.themeCategory) {
        query = query.eq('theme_category', filters.themeCategory);
      }

      if (filters.isPublic !== undefined) {
        query = query.eq('is_public', filters.isPublic);
      }

      // Apply sorting
      const sortBy = filters.sortBy || 'updated_at';
      const sortOrder = filters.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
      }

      const { data: projects, error } = await query;

      if (error) {
        console.error('Error fetching projects:', error);
        return { projects: [], error: new Error(error.message) };
      }

      return { projects: projects || [], error: null };
    } catch (error) {
      console.error('Error fetching projects:', error);
      return { projects: [], error: error as Error };
    }
  }

  /**
   * Get a single project by ID
   */
  static async getProject(projectId: string): Promise<{ project: Project | null; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { project: null, error: new Error('User not authenticated') };
      }

      const { data: project, error } = await supabase
        .from('projects')
        .select('*')
        .eq('id', projectId)
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.error('Error fetching project:', error);
        return { project: null, error: new Error(error.message) };
      }

      return { project, error: null };
    } catch (error) {
      console.error('Error fetching project:', error);
      return { project: null, error: error as Error };
    }
  }

  /**
   * Update a project
   */
  static async updateProject(projectId: string, updates: Partial<ProjectUpdate>): Promise<{ project: Project | null; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { project: null, error: new Error('User not authenticated') };
      }

      const { data: project, error } = await supabase
        .from('projects')
        .update(updates)
        .eq('id', projectId)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating project:', error);
        return { project: null, error: new Error(error.message) };
      }

      return { project, error: null };
    } catch (error) {
      console.error('Error updating project:', error);
      return { project: null, error: error as Error };
    }
  }

  /**
   * Delete a project
   */
  static async deleteProject(projectId: string): Promise<{ error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { error: new Error('User not authenticated') };
      }

      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', projectId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error deleting project:', error);
        return { error: new Error(error.message) };
      }

      return { error: null };
    } catch (error) {
      console.error('Error deleting project:', error);
      return { error: error as Error };
    }
  }

  /**
   * Add an iteration to a project
   */
  static async addProjectIteration(
    projectId: string, 
    userIdea: string, 
    selectedStyle: string, 
    generatedPrompt: string
  ): Promise<{ project: Project | null; error: Error | null }> {
    try {
      const { project: currentProject, error: fetchError } = await this.getProject(projectId);
      
      if (fetchError || !currentProject) {
        return { project: null, error: fetchError || new Error('Project not found') };
      }

      const newIteration: ProjectIteration = {
        id: crypto.randomUUID(),
        userIdea,
        selectedStyle,
        generatedPrompt,
        timestamp: new Date().toISOString()
      };

      const updatedContent: ProjectContent = {
        ...currentProject.content,
        userIdea,
        selectedStyle,
        generatedPrompt,
        iterations: [...(currentProject.content.iterations || []), newIteration],
        metadata: {
          ...currentProject.content.metadata,
          wordCount: generatedPrompt.split(' ').length,
          characterCount: generatedPrompt.length
        }
      };

      return await this.updateProject(projectId, { content: updatedContent });
    } catch (error) {
      console.error('Error adding project iteration:', error);
      return { project: null, error: error as Error };
    }
  }

  /**
   * Toggle project favorite status
   */
  static async toggleProjectFavorite(projectId: string): Promise<{ project: Project | null; error: Error | null }> {
    try {
      const { project: currentProject, error: fetchError } = await this.getProject(projectId);
      
      if (fetchError || !currentProject) {
        return { project: null, error: fetchError || new Error('Project not found') };
      }

      const updatedContent: ProjectContent = {
        ...currentProject.content,
        metadata: {
          ...currentProject.content.metadata,
          isFavorite: !currentProject.content.metadata?.isFavorite
        }
      };

      return await this.updateProject(projectId, { content: updatedContent });
    } catch (error) {
      console.error('Error toggling project favorite:', error);
      return { project: null, error: error as Error };
    }
  }

  /**
   * Get public projects (for browsing community projects)
   */
  static async getPublicProjects(filters: Omit<ProjectFilters, 'isPublic'> = {}): Promise<{ projects: Project[]; error: Error | null }> {
    try {
      let query = supabase
        .from('projects')
        .select('*')
        .eq('is_public', true);

      // Apply filters
      if (filters.search) {
        query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
      }

      if (filters.themeCategory) {
        query = query.eq('theme_category', filters.themeCategory);
      }

      // Apply sorting
      const sortBy = filters.sortBy || 'updated_at';
      const sortOrder = filters.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
      }

      const { data: projects, error } = await query;

      if (error) {
        console.error('Error fetching public projects:', error);
        return { projects: [], error: new Error(error.message) };
      }

      return { projects: projects || [], error: null };
    } catch (error) {
      console.error('Error fetching public projects:', error);
      return { projects: [], error: error as Error };
    }
  }
}

// Utility functions for project operations
export const projectUtils = {
  /**
   * Generate a default project title from user idea
   */
  generateDefaultTitle: (userIdea: string, maxLength: number = 50): string => {
    const cleaned = userIdea.trim();
    if (cleaned.length <= maxLength) return cleaned;
    return cleaned.substring(0, maxLength - 3) + '...';
  },

  /**
   * Format project creation date
   */
  formatDate: (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  },

  /**
   * Get project statistics
   */
  getProjectStats: (project: Project) => {
    // Handle both old schema (with content field) and new schema (without content field)
    const content = (project as any).content;

    if (content && content.metadata) {
      // Old schema with content field
      return {
        wordCount: content.metadata?.wordCount || 0,
        characterCount: content.metadata?.characterCount || 0,
        iterationCount: content.iterations?.length || 0,
        isFavorite: content.metadata?.isFavorite || false,
        tags: content.metadata?.tags || []
      };
    } else {
      // New schema without content field - provide defaults
      return {
        wordCount: 0,
        characterCount: 0,
        iterationCount: 0,
        isFavorite: false,
        tags: []
      };
    }
  },

  /**
   * Validate project data before saving
   */
  validateProjectData: (data: CreateProjectData): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!data.title.trim()) {
      errors.push('Project title is required');
    }

    if (!data.userIdea.trim()) {
      errors.push('User idea is required');
    }

    if (!data.selectedStyle.trim()) {
      errors.push('Prompt style is required');
    }

    if (!data.generatedPrompt.trim()) {
      errors.push('Generated prompt is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
};
